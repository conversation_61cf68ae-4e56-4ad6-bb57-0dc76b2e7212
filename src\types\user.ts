export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  // phone: string;
  // signature: string;
  // lang: string;
  // localZoneId: string;
  // mfaEnabled: string;
  // roles?: string[];
}

export interface UserApiResponse {
  success: boolean;
  data: User;
  message?: string;
}

export interface UserIdApiResponse {
  success: boolean;
  user: User;
  message?: string;
}

import { useState, useEffect } from 'react';
import { User } from '../types/user';
import { getUserDataFromEmail, getMockUserData } from '../services/api';

interface UseUserDataResult {
  user: User | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useUserData = (email: string, useMockData: boolean = true): UseUserDataResult => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      let userData: User;
      
      if (useMockData) {
        // Simulate API delay for realistic experience
        await new Promise(resolve => setTimeout(resolve, 1000));
        userData = getMockUserData();
      } else {
        userData = await getUserDataFromEmail(email);
      }
      
      setUser(userData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';
      setError(errorMessage);
      console.error('Error fetching user data:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (email) {
      fetchUserData();
    }
  }, [email, useMockData]);

  const refetch = () => {
    fetchUserData();
  };

  return {
    user,
    loading,
    error,
    refetch,
  };
};

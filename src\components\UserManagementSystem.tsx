import React, { useState } from 'react';
import { useUserData } from '../hooks/useUserData';
import './UserManagementSystem.css';

interface UserManagementSystemProps {
  email?: string;
  useMockData?: boolean;
}

const UserManagementSystem: React.FC<UserManagementSystemProps> = ({
  email = '<EMAIL>',
  useMockData = false
}) => {
  const { user, loading, error, refetch } = useUserData(email, useMockData);
  const [activeTab, setActiveTab] = useState<'info' | 'campaign'>('info');
  const [userType, setUserType] = useState<'client' | 'partner'>('client');

  if (loading) {
    return (
      <div className="user-management-system">
        <header className="header">
          <h1>User Management System</h1>
        </header>
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>Loading user data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-management-system">
        <header className="header">
          <h1>User Management System</h1>
        </header>
        <div className="error">
          <div className="error-icon">⚠️</div>
          <h3>Error Loading User Data</h3>
          <p>{error}</p>
          <button className="retry-button" onClick={refetch}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="user-management-system">
        <header className="header">
          <h1>User Management System</h1>
        </header>
        <div className="error">
          <div className="error-icon">📭</div>
          <h3>No User Data Found</h3>
          <p>Unable to find user data for the provided email.</p>
          <button className="retry-button" onClick={refetch}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="user-management-system">
      <header className="header">
        <h1>User Management System</h1>
      </header>

      <div className="tabs">
        <button 
          className={`tab ${activeTab === 'info' ? 'active' : ''}`}
          onClick={() => setActiveTab('info')}
        >
          User Info
        </button>
        <button 
          className={`tab ${activeTab === 'campaign' ? 'active' : ''}`}
          onClick={() => setActiveTab('campaign')}
        >
          Active Campaign
        </button>
      </div>

      <div className="user-type-tabs">
        <button 
          className={`user-type-tab ${userType === 'client' ? 'active' : ''}`}
          onClick={() => setUserType('client')}
        >
          Client
        </button>
        <button 
          className={`user-type-tab ${userType === 'partner' ? 'active' : ''}`}
          onClick={() => setUserType('partner')}
        >
          Partner
        </button>
        <div className="actions-button">
          ⚙ Actions
        </div>
      </div>

      {activeTab === 'info' && (
        <div className="content">
          <div className="section">
            <h3>Personal Data</h3>
            <div className="field-grid">
              {/* <div className="field">
                <label>Name (AC)</label>
                <span>{user.name}</span>
              </div> */}
              <div className="field">
                <label>Email</label>
                <span className="email">{user.email}</span>
              </div>
              {/* <div className="field">
                <label>Phone</label>
                <span>{user.phone}</span>
              </div>
              <div className="field">
                <label>Country</label>
                <span>{user.country}</span>
              </div>
              <div className="field">
                <label>LTV</label>
                <span>{user.ltv}</span>
              </div>
              <div className="field">
                <label>First Contact</label>
                <span>{user.firstContact}</span>
              </div> */}
            </div>
          </div>

          <div className="section">
            <h3>Conflict Cure Info</h3>
            {/* <div className="field-grid">
              <div className="field">
                <label>CC Start Date</label>
                <span>{user.ccStartDate}</span>
              </div>
              <div className="field">
                <label>CC Class</label>
                <span>{user.ccClass}</span>
              </div>
              <div className="field">
                <label>Partner Invited</label>
                <span className={user.partnerInvited ? 'yes' : 'no'}>
                  {user.partnerInvited ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="field">
                <label>Partner registered</label>
                <span className={user.partnerRegistered ? 'yes' : 'no'}>
                  {user.partnerRegistered ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="field">
                <label>Partner name</label>
                <span>{user.partnerName}</span>
              </div>
              <div className="field">
                <label>Partner email</label>
                <span className="email">{user.partnerEmail}</span>
              </div>
              <div className="field">
                <label>Asked for refund</label>
                <span className={user.askedForRefund ? 'yes' : 'no'}>
                  {user.askedForRefund ? 'Yes' : 'No'}
                </span>
              </div>
              <div className="field">
                <label>Disputed</label>
                <span className={user.disputed ? 'yes' : 'no'}>
                  {user.disputed ? 'Yes' : 'No'}
                </span>
              </div>
            </div> */}
          </div>

          <div className="section">
            <h3>Order Info</h3>
            <div className="order-info">
              <div className="order-header">
                <span>The Conflict Cure home study program</span>
                <span className="subscription">⭕ Subscription</span>
              </div>
              <div className="order-details">
                <div className="order-row">
                  <span>Mar 20, 2024 $97</span>
                  <span className="status paid">✓ Paid</span>
                  <span className="refund">Refund</span>
                </div>
                <div className="order-row">
                  <span>May 30, 2024</span>
                  <span className="status paused">⏸ Paused</span>
                </div>
              </div>
            </div>
          </div>

          <div className="footer-tabs">
            <span>ActiveCampaign</span>
            <span>ThriveCart</span>
            <span className="active">Stripe</span>
            <span>Paypal</span>
          </div>
        </div>
      )}

      {activeTab === 'campaign' && (
        <div className="content">
          <div className="campaign-placeholder">
            <h3>Active Campaign Information</h3>
            <p>Campaign details would be displayed here...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagementSystem;


const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const path = require('path');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;
const API_KEY = process.env.REACT_APP_API_KEY;

// Check if API key is available
if (!API_KEY) {
  console.warn('API key is not set. API calls may fail.');
}

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`[SERVER] Received request: ${req.method} ${req.url}`);
  res.header('Access-Control-Allow-Origin', '*');
  next();
});

// Test endpoint to verify server is working
app.get('/api/test', (req, res) => {
  console.log('[SERVER] Test endpoint called');
  res.json({ message: 'Server is working!' });
});

// Proxy API requests
app.use('/api/proxy', createProxyMiddleware({
  target: 'https://shreek1123581321.api-us1.com',
  changeOrigin: true,
  pathRewrite: {
    '^/api/proxy': '/api/3', // remove the /api/proxy prefix
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('[SERVER] Proxy endpoint called');
    // Add the API key to the request
    proxyReq.setHeader('Authorization', `Api-Token ${API_KEY}`);
    console.log('[SERVER] Proxying request to baseUrl:', proxyReq.baseUrl);
    console.log('[SERVER] Proxying request to path:', proxyReq.path);
    console.log('[SERVER] With headers:', proxyReq.getHeaders());
  },
  onProxyRes: (proxyRes, req, res) => {
    console.log('[SERVER] Received response with status:', proxyRes.statusCode);
  },
  onError: (err, req, res) => {
    console.error('[SERVER] Proxy error:', err);
    res.status(500).send('Proxy error');
  }
}));

// Serve static files from the React app in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, 'build')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'build', 'index.html'));
  });
}

app.listen(PORT, () => {
  console.log(`Server listening on port ${PORT}`);
  console.log(`Access the app at: http://localhost:${PORT}`);
});

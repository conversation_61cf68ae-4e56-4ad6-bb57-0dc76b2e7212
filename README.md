# User Management System

A TypeScript React application that displays user information by calling two APIs: one to get the user ID from an email, and another to fetch user fields using that ID.

## Features

- **Dual API Integration**: Fetches user ID from email, then retrieves complete user data
- **Modern UI**: Clean, responsive interface matching the provided design
- **TypeScript**: Full type safety throughout the application
- **Error Handling**: Comprehensive error states with retry functionality
- **Loading States**: Smooth loading indicators for better UX
- **Mock Data**: Includes mock data for development and testing

## Project Structure

```
src/
├── components/
│   ├── UserManagementSystem.tsx    # Main component
│   └── UserManagementSystem.css    # Component styles
├── services/
│   └── api.ts                      # API service functions
├── types/
│   └── user.ts                     # TypeScript interfaces
├── hooks/
│   └── useUserData.ts              # Custom hook for data fetching
├── App.tsx                         # Root component
├── App.css                         # Global styles
├── index.tsx                       # Application entry point
└── index.css                       # Base styles
```

## API Integration

### API Endpoints

The application expects two API endpoints:

1. **Get User ID from Email**
   - `POST /users/lookup`
   - Body: `{ "email": "<EMAIL>" }`
   - Response: `{ "success": true, "userId": "user123" }`

2. **Get User Fields by ID**
   - `GET /users/{userId}`
   - Response: `{ "success": true, "data": { ...userFields } }`

### Configuration

To use real APIs instead of mock data:

1. Update the `API_BASE_URL` in `src/services/api.ts`
2. Set `useMockData={false}` in the `UserManagementSystem` component

## Getting Started

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Start the development server:
```bash
npm start
```

3. Open [http://localhost:3000](http://localhost:3000) to view the application

### Available Scripts

- `npm start` - Runs the app in development mode
- `npm test` - Launches the test runner
- `npm run build` - Builds the app for production
- `npm run eject` - Ejects from Create React App (one-way operation)

## Usage

The application displays user information in a tabbed interface:

- **User Info Tab**: Shows personal data, conflict cure information, and order details
- **Active Campaign Tab**: Placeholder for campaign information
- **Client/Partner Toggle**: Switch between client and partner views
- **Actions Menu**: Additional user actions (placeholder)

## Customization

### Styling

The application uses CSS modules with a design system based on:
- Color palette: Blues, grays, and semantic colors
- Typography: System fonts with proper hierarchy
- Layout: Responsive grid system
- Components: Consistent spacing and borders

### Data Structure

User data follows the `User` interface defined in `src/types/user.ts`. Modify this interface to match your API response structure.

## Development Notes

- The application currently uses mock data for demonstration
- Error boundaries and loading states are implemented for robust UX
- The design is responsive and works on mobile devices
- TypeScript ensures type safety throughout the application

## Production Deployment

1. Build the application:
```bash
npm run build
```

2. Deploy the `build` folder to your hosting service

3. Configure your API endpoints in the production environment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

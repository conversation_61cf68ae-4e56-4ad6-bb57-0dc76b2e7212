/* Base font size increase */
.user-management-system {
  max-width: 600px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
  font-size: 1.5em;
}

.header {
  background-color: #4a5568;
  color: white;
  padding: 1rem;
  text-align: center;
}

.header h1 {
  margin: 0;
  font-size: 2.5rem;
  font-weight: 600;
}

.tabs {
  display: flex;
  background-color: #e2e8f0;
  border-bottom: 1px solid #cbd5e0;
}

.tab {
  flex: 1;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.5rem;
  color: #4a5568;
  border-bottom: 3px solid transparent;
}

.tab.active {
  color: #3182ce;
  border-bottom-color: #3182ce;
  background-color: white;
}

.user-type-tabs {
  display: flex;
  align-items: center;
  background-color: white;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  gap: 1rem;
}

.user-type-tab {
  padding: 0.5rem 1rem;
  background: none;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1.3rem;
  color: #4a5568;
}

.user-type-tab.active {
  background-color: #3182ce;
  color: white;
  border-color: #3182ce;
}

.actions-button {
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: #f7fafc;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 1.3rem;
  color: #4a5568;
  cursor: pointer;
}

.content {
  background-color: white;
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
}

.section h3 {
  margin: 0 0 1rem 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
}

.field-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: flex-start;
  width: 100%;
}

.field {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
  width: 100%;
  justify-content: flex-start;
}

.field label {
  font-size: 1.3rem;
  color: #4a5568;
  font-weight: 500;
  text-align: left;
  width: auto;
  min-width: 150px;
}

.field span {
  font-size: 1.3rem;
  color: #2d3748;
  text-align: left;
}

.field span.email {
  color: #3182ce;
}

.field span.yes {
  color: #38a169;
}

.field span.no {
  color: #e53e3e;
}

.order-info {
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.order-header {
  background-color: #f7fafc;
  padding: 0.75rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  font-size: 1.3rem;
}

.subscription {
  background-color: #fed7d7;
  color: #c53030;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 1.1rem;
}

.order-details {
  padding: 1rem;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
  font-size: 1.3rem;
}

.order-row:last-child {
  border-bottom: none;
}

.status {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 1.2rem;
  font-weight: 500;
}

.status.paid {
  background-color: #c6f6d5;
  color: #22543d;
}

.status.paused {
  background-color: #fed7d7;
  color: #c53030;
}

.refund {
  color: #3182ce;
  cursor: pointer;
  font-size: 1.3rem;
}

.footer-tabs {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.footer-tabs span {
  font-size: 1.3rem;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem 0;
}

.footer-tabs span.active {
  color: #3182ce;
  border-bottom: 2px solid #3182ce;
}

.loading, .error {
  text-align: center;
  padding: 2rem;
  font-size: 1.7rem;
}

.error {
  color: #e53e3e;
}

.campaign-placeholder {
  text-align: center;
  padding: 3rem 1rem;
  color: #718096;
}

@media (max-width: 768px) {
  .field-grid {
    grid-template-columns: 1fr;
  }
  
  .user-type-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .order-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .footer-tabs {
    flex-wrap: wrap;
    gap: 1rem;
  }
}




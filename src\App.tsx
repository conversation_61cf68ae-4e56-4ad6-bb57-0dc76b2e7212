import React from 'react';
import UserManagementSystem from './components/UserManagementSystem';
import './App.css';

function App() {
  // Get email from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const email = urlParams.get('email') || '<EMAIL>'; // Default if not provided

  return (
    <div className="App">
      <UserManagementSystem email={email} />
    </div>
  );
}

export default App;


import { User, UserApiResponse, UserIdApiResponse } from '../types/user';

// Update API base URL to use local server proxy
const API_BASE_URL = '/api/proxy';
const API_KEY = process.env.REACT_APP_API_KEY || '';

// Check if API key is available
if (!API_KEY) {
  console.warn('API key is not set. API calls may fail.');
}

// Add debugging to see what URL is being called
export const getUserIdFromEmail = async (email: string): Promise<number> => {
  try {
    const url = `${API_BASE_URL}/users/email/${email}`;
    console.log('Calling API:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: UserIdApiResponse = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Failed to get user ID');
    }

    return data.user.id;
  } catch (error) {
    console.error('Error fetching user ID:', error);
    throw error;
  }
};

/**
 * Get user fields/data using user ID
 * @param userId - User's unique identifier
 * @returns Promise with user data
 */
export const getUserFields = async (userId: number): Promise<User> => {
  try {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Api-Token ${API_KEY}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: UserApiResponse = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'Failed to get user data');
    }

    return data.data;
  } catch (error) {
    console.error('Error fetching user data:', error);
    throw error;
  }
};

/**
 * Combined function to get user data from email
 * @param email - User's email address
 * @returns Promise with complete user data
 */
export const getUserDataFromEmail = async (email: string): Promise<User> => {
  try {
    const userId = await getUserIdFromEmail(email);
    const userData = await getUserFields(userId);
    return userData;
  } catch (error) {
    console.error('Error in getUserDataFromEmail:', error);
    throw error;
  }
};

// Mock data for development/testing
export const getMockUserData = (): User => {
  return {
    id: 666,
    email: '<EMAIL>',
    firstName: 'Diana',
    lastName: 'Bentol',
    // phone: '+0034664145684'
  };
};



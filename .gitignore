# Dependencies
node_modules/*
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
build/
dist/
*.tgz
*.tar.gz

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release

# Users Environment Variables
.lock-wscript

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# React specific
build/
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
.nyc_output/

# Storybook build outputs
storybook-static

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Package manager lock files (choose one to keep)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Local environment files
.env*.local

# Vercel
.vercel

# Typescript
*.tsbuildinfo

# ESLint cache
.eslintcache

# Stylelint cache
.stylelintcache

# Prettier cache
.prettiercache

# Bundle analyzer
bundle-analyzer-report.html

# Local development
.local

# Backup files
*.bak
*.backup
*.old

# Documentation build
docs/build/

# Miscellaneous
*.tgz
*.tar.gz
*.zip
*.rar
*.7z

# Database
*.db
*.sqlite
*.sqlite3

# Configuration files with sensitive data
config/local.json
config/production.json
secrets.json

# API keys and secrets
.env
.env.local
.env.development
.env.test
.env.production
.env*.local
api-keys.json
secrets.json

# Build artifacts
*.map
*.min.js
*.min.css

# Cache directories
.cache/
.tmp/
.temp/

# Lock files (uncomment the ones you want to ignore)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml



{"name": "management-system", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.0", "web-vitals": "^2.1.0", "dotenv": "^16.0.3", "express": "^4.18.2", "http-proxy-middleware": "^2.0.6", "concurrently": "^7.6.0"}, "proxy": "http://localhost:3001", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "server": "node server.js", "dev": "concurrently \"npm run server\" \"npm run start\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}